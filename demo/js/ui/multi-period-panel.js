/**
 * 多周期参数设置面板
 * 提供图形化界面设置多周期扫描条件
 */

class MultiPeriodPanel {
    constructor(containerId, config = null) {
        this.containerId = containerId;
        this.config = config || window.INDICATOR_CONFIG;
        this.multiPeriodConfig = this.config.getMultiPeriodScanConfig();
        
        this.isVisible = false;
        this.currentConditions = this.getDefaultConditions();
        
        this.init();
    }

    /**
     * 初始化面板
     */
    init() {
        this.createPanel();
        this.bindEvents();
    }

    /**
     * 创建面板HTML
     */
    createPanel() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error(`容器 ${this.containerId} 不存在`);
            return;
        }

        container.innerHTML = `
            <div class="multi-period-panel card p-6" style="display: none;">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-semibold">多周期扫描设置</h3>
                    <button class="btn-secondary text-sm" onclick="this.closest('.multi-period-panel').style.display='none'">
                        <i class="fas fa-times mr-1"></i>关闭
                    </button>
                </div>

                <!-- 逻辑关系选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-400 mb-3">条件组合逻辑</label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="logic" value="AND" checked class="mr-2">
                            <span class="text-sm">全部满足（AND）</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="logic" value="OR" class="mr-2">
                            <span class="text-sm">任一满足（OR）</span>
                        </label>
                    </div>
                </div>

                <!-- 周期条件设置 -->
                <div class="space-y-6" id="periodConditions">
                    ${this.generatePeriodSections()}
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-between mt-8">
                    <div class="space-x-2">
                        <button class="btn-secondary" id="resetConditions">
                            <i class="fas fa-undo mr-1"></i>重置
                        </button>
                        <button class="btn-secondary" id="presetConditions">
                            <i class="fas fa-magic mr-1"></i>预设条件
                        </button>
                    </div>
                    <div class="space-x-2">
                        <button class="btn-secondary" id="previewConditions">
                            <i class="fas fa-eye mr-1"></i>预览
                        </button>
                        <button class="btn-primary" id="applyConditions">
                            <i class="fas fa-check mr-1"></i>应用设置
                        </button>
                    </div>
                </div>

                <!-- 预设条件下拉菜单 -->
                <div class="preset-menu card absolute z-50 mt-2 p-4 shadow-xl" style="display: none;" id="presetMenu">
                    <div class="space-y-2">
                        <button class="w-full text-left p-2 hover:bg-gray-700 rounded preset-item" data-preset="bullish">
                            <div class="font-medium">多头趋势</div>
                            <div class="text-xs text-gray-400">日线KDJ买点 + 周月线MACD上升</div>
                        </button>
                        <button class="w-full text-left p-2 hover:bg-gray-700 rounded preset-item" data-preset="conservative">
                            <div class="font-medium">保守策略</div>
                            <div class="text-xs text-gray-400">所有周期MACD金叉</div>
                        </button>
                        <button class="w-full text-left p-2 hover:bg-gray-700 rounded preset-item" data-preset="aggressive">
                            <div class="font-medium">激进策略</div>
                            <div class="text-xs text-gray-400">日线强势信号</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 生成周期设置区域
     */
    generatePeriodSections() {
        const periods = ['daily', 'weekly', 'monthly'];
        const periodNames = this.multiPeriodConfig.periodNames;
        
        return periods.map(period => `
            <div class="period-section border border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium">${periodNames[period]}</h4>
                    <label class="flex items-center">
                        <input type="checkbox" class="period-enabled mr-2" data-period="${period}" checked>
                        <span class="text-sm text-gray-400">启用</span>
                    </label>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- KDJ设置 -->
                    <div class="indicator-setting">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" class="indicator-enabled mr-2" data-period="${period}" data-indicator="kdj">
                            <span class="text-sm font-medium">KDJ指标</span>
                        </label>
                        <select class="input-field w-full text-sm condition-select" data-period="${period}" data-indicator="kdj">
                            <option value="j_above_50">J值大于50</option>
                            <option value="j_above_80">J值大于80（超买）</option>
                            <option value="j_below_20">J值小于20（超卖）</option>
                            <option value="golden_cross">KDJ金叉</option>
                            <option value="death_cross">KDJ死叉</option>
                        </select>
                    </div>

                    <!-- MACD设置 -->
                    <div class="indicator-setting">
                        <label class="flex items-center mb-2">
                            <input type="checkbox" class="indicator-enabled mr-2" data-period="${period}" data-indicator="macd">
                            <span class="text-sm font-medium">MACD指标</span>
                        </label>
                        <select class="input-field w-full text-sm condition-select" data-period="${period}" data-indicator="macd">
                            <option value="golden_cross">MACD金叉</option>
                            <option value="death_cross">MACD死叉</option>
                            <option value="above_zero">DIF在零轴上方</option>
                            <option value="below_zero">DIF在零轴下方</option>
                            <option value="bullish_trend">多头趋势</option>
                            <option value="bearish_trend">空头趋势</option>
                        </select>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const panel = document.querySelector('.multi-period-panel');
        if (!panel) return;

        // 应用设置按钮
        panel.querySelector('#applyConditions').addEventListener('click', () => {
            this.applyConditions();
        });

        // 重置按钮
        panel.querySelector('#resetConditions').addEventListener('click', () => {
            this.resetConditions();
        });

        // 预设条件按钮
        panel.querySelector('#presetConditions').addEventListener('click', (e) => {
            this.togglePresetMenu(e);
        });

        // 预览按钮
        panel.querySelector('#previewConditions').addEventListener('click', () => {
            this.previewConditions();
        });

        // 预设条件选择
        panel.querySelectorAll('.preset-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const preset = e.currentTarget.dataset.preset;
                this.applyPreset(preset);
                this.hidePresetMenu();
            });
        });

        // 条件变化监听
        panel.addEventListener('change', (e) => {
            if (e.target.matches('.period-enabled, .indicator-enabled, .condition-select, input[name="logic"]')) {
                this.updateConditions();
            }
        });

        // 点击外部关闭预设菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('#presetConditions') && !e.target.closest('#presetMenu')) {
                this.hidePresetMenu();
            }
        });
    }

    /**
     * 显示面板
     */
    show() {
        const panel = document.querySelector('.multi-period-panel');
        if (panel) {
            panel.style.display = 'block';
            this.isVisible = true;
            this.loadCurrentConditions();
        }
    }

    /**
     * 隐藏面板
     */
    hide() {
        const panel = document.querySelector('.multi-period-panel');
        if (panel) {
            panel.style.display = 'none';
            this.isVisible = false;
        }
    }

    /**
     * 切换面板显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 获取默认条件
     */
    getDefaultConditions() {
        return {
            logic: 'AND',
            periods: ['daily', 'weekly', 'monthly'],
            conditions: {
                daily: {
                    kdj: { enabled: true, type: 'j_above_50' },
                    macd: { enabled: false, type: 'golden_cross' }
                },
                weekly: {
                    kdj: { enabled: false, type: 'j_above_50' },
                    macd: { enabled: true, type: 'above_zero' }
                },
                monthly: {
                    kdj: { enabled: false, type: 'j_above_50' },
                    macd: { enabled: true, type: 'above_zero' }
                }
            }
        };
    }

    /**
     * 加载当前条件到界面
     */
    loadCurrentConditions() {
        const panel = document.querySelector('.multi-period-panel');
        if (!panel) return;

        // 设置逻辑关系
        const logicRadio = panel.querySelector(`input[name="logic"][value="${this.currentConditions.logic}"]`);
        if (logicRadio) logicRadio.checked = true;

        // 设置各周期条件
        Object.keys(this.currentConditions.conditions).forEach(period => {
            const periodConditions = this.currentConditions.conditions[period];
            
            // 设置周期启用状态
            const periodEnabled = panel.querySelector(`.period-enabled[data-period="${period}"]`);
            if (periodEnabled) {
                periodEnabled.checked = this.currentConditions.periods.includes(period);
            }

            // 设置指标条件
            Object.keys(periodConditions).forEach(indicator => {
                const indicatorCondition = periodConditions[indicator];
                
                const indicatorEnabled = panel.querySelector(`.indicator-enabled[data-period="${period}"][data-indicator="${indicator}"]`);
                if (indicatorEnabled) {
                    indicatorEnabled.checked = indicatorCondition.enabled;
                }

                const conditionSelect = panel.querySelector(`.condition-select[data-period="${period}"][data-indicator="${indicator}"]`);
                if (conditionSelect) {
                    conditionSelect.value = indicatorCondition.type;
                }
            });
        });
    }

    /**
     * 更新条件配置
     */
    updateConditions() {
        const panel = document.querySelector('.multi-period-panel');
        if (!panel) return;

        // 获取逻辑关系
        const logicRadio = panel.querySelector('input[name="logic"]:checked');
        this.currentConditions.logic = logicRadio ? logicRadio.value : 'AND';

        // 获取启用的周期
        this.currentConditions.periods = [];
        panel.querySelectorAll('.period-enabled:checked').forEach(checkbox => {
            this.currentConditions.periods.push(checkbox.dataset.period);
        });

        // 获取各周期的指标条件
        ['daily', 'weekly', 'monthly'].forEach(period => {
            ['kdj', 'macd'].forEach(indicator => {
                const enabledCheckbox = panel.querySelector(`.indicator-enabled[data-period="${period}"][data-indicator="${indicator}"]`);
                const conditionSelect = panel.querySelector(`.condition-select[data-period="${period}"][data-indicator="${indicator}"]`);
                
                if (enabledCheckbox && conditionSelect) {
                    this.currentConditions.conditions[period][indicator] = {
                        enabled: enabledCheckbox.checked,
                        type: conditionSelect.value
                    };
                }
            });
        });
    }

    /**
     * 应用条件设置
     */
    applyConditions() {
        this.updateConditions();
        
        // 触发条件应用事件
        if (this.onConditionsApply) {
            this.onConditionsApply(this.currentConditions);
        }

        // 显示成功提示
        this.showNotification('扫描条件已应用', 'success');
        this.hide();
    }

    /**
     * 重置条件
     */
    resetConditions() {
        this.currentConditions = this.getDefaultConditions();
        this.loadCurrentConditions();
        this.showNotification('条件已重置为默认值', 'info');
    }

    /**
     * 切换预设菜单
     */
    togglePresetMenu(e) {
        const menu = document.getElementById('presetMenu');
        if (menu) {
            const isVisible = menu.style.display !== 'none';
            if (isVisible) {
                this.hidePresetMenu();
            } else {
                const rect = e.target.getBoundingClientRect();
                menu.style.display = 'block';
                menu.style.left = rect.left + 'px';
                menu.style.top = (rect.bottom + 5) + 'px';
            }
        }
    }

    /**
     * 隐藏预设菜单
     */
    hidePresetMenu() {
        const menu = document.getElementById('presetMenu');
        if (menu) {
            menu.style.display = 'none';
        }
    }

    /**
     * 应用预设条件
     */
    applyPreset(preset) {
        const presets = {
            bullish: {
                logic: 'AND',
                periods: ['daily', 'weekly', 'monthly'],
                conditions: {
                    daily: {
                        kdj: { enabled: true, type: 'j_above_50' },
                        macd: { enabled: false, type: 'golden_cross' }
                    },
                    weekly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: true, type: 'above_zero' }
                    },
                    monthly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: true, type: 'above_zero' }
                    }
                }
            },
            conservative: {
                logic: 'AND',
                periods: ['daily', 'weekly', 'monthly'],
                conditions: {
                    daily: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: true, type: 'golden_cross' }
                    },
                    weekly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: true, type: 'golden_cross' }
                    },
                    monthly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: true, type: 'golden_cross' }
                    }
                }
            },
            aggressive: {
                logic: 'OR',
                periods: ['daily'],
                conditions: {
                    daily: {
                        kdj: { enabled: true, type: 'j_above_80' },
                        macd: { enabled: true, type: 'golden_cross' }
                    },
                    weekly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: false, type: 'above_zero' }
                    },
                    monthly: {
                        kdj: { enabled: false, type: 'j_above_50' },
                        macd: { enabled: false, type: 'above_zero' }
                    }
                }
            }
        };

        if (presets[preset]) {
            this.currentConditions = { ...presets[preset] };
            this.loadCurrentConditions();
            this.showNotification(`已应用${preset}预设条件`, 'success');
        }
    }

    /**
     * 预览条件
     */
    previewConditions() {
        this.updateConditions();
        
        const summary = this.generateConditionSummary();
        alert(`扫描条件预览:\n\n${summary}`);
    }

    /**
     * 生成条件摘要
     */
    generateConditionSummary() {
        const periodNames = this.multiPeriodConfig.periodNames;
        const conditionTypes = this.multiPeriodConfig.conditionTypes;
        
        let summary = `逻辑关系: ${this.currentConditions.logic === 'AND' ? '全部满足' : '任一满足'}\n\n`;
        
        this.currentConditions.periods.forEach(period => {
            const periodConditions = this.currentConditions.conditions[period];
            const enabledConditions = [];
            
            Object.keys(periodConditions).forEach(indicator => {
                const condition = periodConditions[indicator];
                if (condition.enabled) {
                    const conditionName = conditionTypes[indicator][condition.type];
                    enabledConditions.push(`${indicator.toUpperCase()}: ${conditionName}`);
                }
            });
            
            if (enabledConditions.length > 0) {
                summary += `${periodNames[period]}:\n${enabledConditions.join('\n')}\n\n`;
            }
        });
        
        return summary;
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        // 这里可以集成现有的通知系统
        console.log(`[${type.toUpperCase()}] ${message}`);
    }

    /**
     * 获取当前条件
     */
    getCurrentConditions() {
        return { ...this.currentConditions };
    }

    /**
     * 设置条件应用回调
     */
    setOnConditionsApply(callback) {
        this.onConditionsApply = callback;
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiPeriodPanel;
}
