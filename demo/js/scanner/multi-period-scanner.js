/**
 * 多周期扫描器
 * 实现多周期多指标组合扫描逻辑
 * 支持日线、周线、月线的KDJ和MACD指标组合分析
 */

class MultiPeriodScanner {
    constructor(dataSimulator, config = null) {
        this.dataSimulator = dataSimulator;
        this.config = config || window.INDICATOR_CONFIG;
        
        // 初始化指标计算器
        this.indicators = {
            kdj: new KDJIndicator(this.config.indicators.kdj),
            macd: new MACDIndicator(this.config.indicators.macd),
            bollinger: new BollingerBands(this.config.indicators.bollinger),
            volume: new VolumeAnalysis(this.config.indicators.volume)
        };

        // 扫描状态
        this.isScanning = false;
        this.scanResults = [];
        this.scanProgress = { current: 0, total: 0, percentage: 0 };
        this.scanStartTime = null;

        // 多周期扫描配置
        this.multiPeriodConfig = this.config.getMultiPeriodScanConfig();
        
        // 默认扫描条件
        this.scanConditions = {
            periods: ['daily', 'weekly', 'monthly'],
            logic: 'AND', // AND 或 OR
            conditions: {
                daily: {
                    kdj: { enabled: true, type: 'j_above_50', threshold: 50 },
                    macd: { enabled: false, type: 'golden_cross', threshold: 0 }
                },
                weekly: {
                    kdj: { enabled: false, type: 'j_above_50', threshold: 50 },
                    macd: { enabled: true, type: 'above_zero', threshold: 0 }
                },
                monthly: {
                    kdj: { enabled: false, type: 'j_above_50', threshold: 50 },
                    macd: { enabled: true, type: 'above_zero', threshold: 0 }
                }
            }
        };
    }

    /**
     * 设置扫描条件
     * @param {Object} conditions - 扫描条件配置
     */
    setScanConditions(conditions) {
        this.scanConditions = { ...this.scanConditions, ...conditions };
    }

    /**
     * 开始多周期扫描
     * @param {Object} options - 扫描选项
     * @returns {Promise} 扫描结果
     */
    async startScan(options = {}) {
        if (this.isScanning) {
            throw new Error('扫描正在进行中');
        }

        this.isScanning = true;
        this.scanStartTime = Date.now();
        this.scanResults = [];

        try {
            const stockList = this.dataSimulator.stockList;
            const totalStocks = stockList.length;
            
            this.scanProgress = {
                current: 0,
                total: totalStocks,
                percentage: 0
            };

            // 批量获取多周期数据
            const stockCodes = stockList.map(stock => stock.code);
            const periods = this.scanConditions.periods;
            const days = options.days || 120;

            console.log(`开始多周期扫描，股票数量: ${totalStocks}, 周期: ${periods.join(', ')}`);

            // 分批处理以提高性能
            const batchSize = 10;
            const batches = [];
            for (let i = 0; i < stockCodes.length; i += batchSize) {
                batches.push(stockCodes.slice(i, i + batchSize));
            }

            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
                const batch = batches[batchIndex];
                const batchResults = await this.processBatch(batch, periods, days);
                
                // 筛选满足条件的股票
                const filteredResults = batchResults.filter(result => result.matchesConditions);
                this.scanResults.push(...filteredResults);

                // 更新进度
                this.scanProgress.current = Math.min((batchIndex + 1) * batchSize, totalStocks);
                this.scanProgress.percentage = Math.round((this.scanProgress.current / totalStocks) * 100);

                // 触发进度更新事件
                this.onProgressUpdate && this.onProgressUpdate(this.scanProgress);

                // 让出控制权，避免阻塞UI
                await new Promise(resolve => setTimeout(resolve, 10));
            }

            const scanDuration = Date.now() - this.scanStartTime;
            console.log(`多周期扫描完成，耗时: ${scanDuration}ms, 找到 ${this.scanResults.length} 只符合条件的股票`);

            return {
                results: this.scanResults,
                summary: {
                    totalScanned: totalStocks,
                    matchedCount: this.scanResults.length,
                    scanDuration: scanDuration,
                    conditions: this.scanConditions
                }
            };

        } catch (error) {
            console.error('多周期扫描失败:', error);
            throw error;
        } finally {
            this.isScanning = false;
        }
    }

    /**
     * 处理一批股票
     * @param {Array} stockCodes - 股票代码数组
     * @param {Array} periods - 周期数组
     * @param {number} days - 数据天数
     * @returns {Array} 批处理结果
     */
    async processBatch(stockCodes, periods, days) {
        const batchResults = [];

        for (const stockCode of stockCodes) {
            try {
                const stockInfo = this.dataSimulator.stockList.find(s => s.code === stockCode);
                if (!stockInfo) continue;

                // 获取多周期数据
                const multiPeriodData = this.dataSimulator.getBatchMultiPeriodData([stockCode], days, periods);
                const stockData = multiPeriodData[stockCode];

                if (!stockData) continue;

                // 分析各周期指标
                const analysis = await this.analyzeMultiPeriod(stockCode, stockData);
                
                // 检查是否满足扫描条件
                const matchesConditions = this.checkConditions(analysis);

                if (matchesConditions) {
                    batchResults.push({
                        stockCode: stockCode,
                        stockInfo: stockInfo,
                        analysis: analysis,
                        matchesConditions: true,
                        scanTime: new Date().toISOString()
                    });
                }

            } catch (error) {
                console.error(`处理股票 ${stockCode} 失败:`, error);
            }
        }

        return batchResults;
    }

    /**
     * 分析多周期指标
     * @param {string} stockCode - 股票代码
     * @param {Object} stockData - 多周期股票数据
     * @returns {Object} 分析结果
     */
    async analyzeMultiPeriod(stockCode, stockData) {
        const analysis = {
            stockCode: stockCode,
            periods: {}
        };

        for (const period of this.scanConditions.periods) {
            if (!stockData[period] || !stockData[period].data) continue;

            const periodData = stockData[period].data;
            const periodAnalysis = {
                period: period,
                dataPoints: periodData.dates.length,
                indicators: {}
            };

            try {
                // 计算KDJ指标
                if (this.scanConditions.conditions[period].kdj.enabled) {
                    const kdjResult = this.indicators.kdj.calculate({
                        high: periodData.high,
                        low: periodData.low,
                        close: periodData.close
                    });
                    
                    periodAnalysis.indicators.kdj = {
                        current: {
                            k: kdjResult.k[kdjResult.k.length - 1],
                            d: kdjResult.d[kdjResult.d.length - 1],
                            j: kdjResult.j[kdjResult.j.length - 1]
                        },
                        signals: kdjResult.signals
                    };
                }

                // 计算MACD指标
                if (this.scanConditions.conditions[period].macd.enabled) {
                    const macdResult = this.indicators.macd.calculate(periodData.close);
                    
                    periodAnalysis.indicators.macd = {
                        current: {
                            dif: macdResult.dif[macdResult.dif.length - 1],
                            dea: macdResult.dea[macdResult.dea.length - 1],
                            macd: macdResult.macd[macdResult.macd.length - 1]
                        },
                        signals: macdResult.signals
                    };
                }

            } catch (error) {
                console.error(`计算 ${stockCode} ${period} 周期指标失败:`, error);
            }

            analysis.periods[period] = periodAnalysis;
        }

        return analysis;
    }

    /**
     * 检查是否满足扫描条件
     * @param {Object} analysis - 分析结果
     * @returns {boolean} 是否满足条件
     */
    checkConditions(analysis) {
        const conditionResults = [];

        for (const period of this.scanConditions.periods) {
            const periodAnalysis = analysis.periods[period];
            if (!periodAnalysis) continue;

            const periodConditions = this.scanConditions.conditions[period];
            const periodResults = [];

            // 检查KDJ条件
            if (periodConditions.kdj.enabled && periodAnalysis.indicators.kdj) {
                const kdjResult = this.checkKDJCondition(
                    periodAnalysis.indicators.kdj,
                    periodConditions.kdj
                );
                periodResults.push(kdjResult);
            }

            // 检查MACD条件
            if (periodConditions.macd.enabled && periodAnalysis.indicators.macd) {
                const macdResult = this.checkMACDCondition(
                    periodAnalysis.indicators.macd,
                    periodConditions.macd
                );
                periodResults.push(macdResult);
            }

            // 周期内条件组合（默认AND）
            const periodMatch = periodResults.length > 0 && periodResults.every(r => r);
            conditionResults.push(periodMatch);
        }

        // 跨周期条件组合
        if (this.scanConditions.logic === 'OR') {
            return conditionResults.some(r => r);
        } else {
            return conditionResults.length > 0 && conditionResults.every(r => r);
        }
    }

    /**
     * 检查KDJ条件
     * @param {Object} kdjData - KDJ数据
     * @param {Object} condition - 条件配置
     * @returns {boolean} 是否满足条件
     */
    checkKDJCondition(kdjData, condition) {
        const { current } = kdjData;
        const { type, threshold } = condition;

        switch (type) {
            case 'j_above_50':
                return current.j > 50;
            case 'j_above_80':
                return current.j > 80;
            case 'j_below_20':
                return current.j < 20;
            case 'golden_cross':
                return kdjData.signals.goldenCross.length > 0;
            case 'death_cross':
                return kdjData.signals.deathCross.length > 0;
            default:
                return false;
        }
    }

    /**
     * 检查MACD条件
     * @param {Object} macdData - MACD数据
     * @param {Object} condition - 条件配置
     * @returns {boolean} 是否满足条件
     */
    checkMACDCondition(macdData, condition) {
        const { current, signals } = macdData;
        const { type, threshold } = condition;

        switch (type) {
            case 'golden_cross':
                return signals.goldenCross.length > 0;
            case 'death_cross':
                return signals.deathCross.length > 0;
            case 'above_zero':
                return current.dif > 0;
            case 'below_zero':
                return current.dif < 0;
            case 'bullish_trend':
                return signals.current.trend === 'bullish';
            case 'bearish_trend':
                return signals.current.trend === 'bearish';
            default:
                return false;
        }
    }

    /**
     * 停止扫描
     */
    stopScan() {
        this.isScanning = false;
    }

    /**
     * 获取扫描进度
     * @returns {Object} 扫描进度
     */
    getScanProgress() {
        return { ...this.scanProgress };
    }

    /**
     * 获取扫描结果
     * @returns {Array} 扫描结果
     */
    getScanResults() {
        return [...this.scanResults];
    }

    /**
     * 清除扫描结果
     */
    clearResults() {
        this.scanResults = [];
        this.scanProgress = { current: 0, total: 0, percentage: 0 };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiPeriodScanner;
}
