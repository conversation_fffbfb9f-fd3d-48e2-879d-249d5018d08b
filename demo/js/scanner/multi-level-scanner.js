/**
 * 多层级筛选扫描器
 * 实现逐层筛选机制，从粗筛到精筛的优化算法
 * 支持日线→周线→月线的逐层复筛
 */

class MultiLevelScanner {
    constructor(dataSimulator, config = null) {
        this.dataSimulator = dataSimulator;
        this.config = config || window.INDICATOR_CONFIG;
        
        // 初始化指标计算器
        this.indicators = {
            kdj: new KDJIndicator(this.config.indicators.kdj),
            macd: new MACDIndicator(this.config.indicators.macd),
            bollinger: new BollingerBands(this.config.indicators.bollinger),
            volume: new VolumeAnalysis(this.config.indicators.volume)
        };

        // 扫描状态
        this.isScanning = false;
        this.scanStartTime = null;
        
        // 多层级配置
        this.levels = [
            {
                name: '初筛',
                period: 'daily',
                description: '日线基础筛选',
                indicators: ['kdj'],
                conditions: {
                    kdj: { type: 'j_above_50', threshold: 50 }
                },
                color: '#3B82F6'
            },
            {
                name: '复筛1',
                period: 'weekly', 
                description: '周线金叉筛选',
                indicators: ['kdj'],
                conditions: {
                    kdj: { type: 'golden_cross', threshold: 0 }
                },
                color: '#10B981'
            },
            {
                name: '复筛2',
                period: 'monthly',
                description: '月线趋势筛选', 
                indicators: ['kdj'],
                conditions: {
                    kdj: { type: 'j_above_50', threshold: 50 }
                },
                color: '#F59E0B'
            }
        ];

        // 筛选结果
        this.levelResults = [];
        this.finalResults = [];
        
        // 进度跟踪
        this.currentLevel = 0;
        this.levelProgress = { current: 0, total: 0, percentage: 0 };
        
        // 缓存机制
        this.dataCache = new Map();
        this.indicatorCache = new Map();
    }

    /**
     * 设置筛选层级配置
     * @param {Array} levels - 层级配置数组
     */
    setLevels(levels) {
        this.levels = levels;
    }

    /**
     * 开始多层级筛选
     * @param {Object} options - 扫描选项
     * @returns {Promise} 筛选结果
     */
    async startMultiLevelScan(options = {}) {
        if (this.isScanning) {
            throw new Error('筛选正在进行中');
        }

        this.isScanning = true;
        this.scanStartTime = Date.now();
        this.levelResults = [];
        this.finalResults = [];
        this.currentLevel = 0;

        try {
            // 获取初始股票列表
            let candidates = this.dataSimulator.stockList.map(stock => ({
                code: stock.code,
                name: stock.name,
                market: stock.market
            }));

            console.log(`开始多层级筛选，初始股票数量: ${candidates.length}`);
            console.log(`筛选层级: ${this.levels.map(l => l.name).join(' → ')}`);

            // 逐层筛选
            for (let levelIndex = 0; levelIndex < this.levels.length; levelIndex++) {
                this.currentLevel = levelIndex;
                const level = this.levels[levelIndex];
                
                console.log(`\n=== ${level.name} (${level.period}) ===`);
                console.log(`输入股票数量: ${candidates.length}`);

                if (candidates.length === 0) {
                    console.log('候选股票为空，停止筛选');
                    break;
                }

                // 处理当前层级
                const levelResult = await this.processLevel(level, candidates, options);
                this.levelResults.push(levelResult);
                
                // 更新候选股票为当前层级的输出
                candidates = levelResult.passedStocks;
                
                console.log(`输出股票数量: ${candidates.length}`);
                console.log(`筛选率: ${((candidates.length / levelResult.inputCount) * 100).toFixed(1)}%`);

                // 触发层级完成事件
                this.onLevelComplete && this.onLevelComplete(levelIndex, levelResult);

                // 如果没有股票通过，提前结束
                if (candidates.length === 0) {
                    console.log('没有股票通过筛选，停止后续层级');
                    break;
                }
            }

            this.finalResults = candidates;
            const scanDuration = Date.now() - this.scanStartTime;
            
            console.log(`\n=== 多层级筛选完成 ===`);
            console.log(`总耗时: ${scanDuration}ms`);
            console.log(`最终结果: ${this.finalResults.length} 只股票`);

            return {
                levels: this.levelResults,
                finalResults: this.finalResults,
                summary: {
                    totalLevels: this.levels.length,
                    initialCount: this.dataSimulator.stockList.length,
                    finalCount: this.finalResults.length,
                    scanDuration: scanDuration,
                    overallFilterRate: ((this.finalResults.length / this.dataSimulator.stockList.length) * 100).toFixed(2) + '%'
                }
            };

        } catch (error) {
            console.error('多层级筛选失败:', error);
            throw error;
        } finally {
            this.isScanning = false;
        }
    }

    /**
     * 处理单个筛选层级
     * @param {Object} level - 层级配置
     * @param {Array} candidates - 候选股票
     * @param {Object} options - 选项
     * @returns {Object} 层级结果
     */
    async processLevel(level, candidates, options = {}) {
        const startTime = Date.now();
        const inputCount = candidates.length;
        const passedStocks = [];
        const failedStocks = [];
        
        // 初始化进度
        this.levelProgress = {
            current: 0,
            total: inputCount,
            percentage: 0
        };

        const days = options.days || 120;
        const batchSize = 20; // 批处理大小

        // 分批处理
        for (let i = 0; i < candidates.length; i += batchSize) {
            const batch = candidates.slice(i, i + batchSize);
            const batchResults = await this.processBatch(level, batch, days);
            
            // 分类结果
            batchResults.forEach(result => {
                if (result.passed) {
                    passedStocks.push(result.stock);
                } else {
                    failedStocks.push(result.stock);
                }
            });

            // 更新进度
            this.levelProgress.current = Math.min(i + batchSize, inputCount);
            this.levelProgress.percentage = Math.round((this.levelProgress.current / inputCount) * 100);
            
            // 触发进度更新事件
            this.onLevelProgress && this.onLevelProgress(this.currentLevel, this.levelProgress);

            // 让出控制权，避免阻塞UI
            await new Promise(resolve => setTimeout(resolve, 5));
        }

        const processingTime = Date.now() - startTime;
        
        return {
            levelIndex: this.currentLevel,
            levelName: level.name,
            period: level.period,
            description: level.description,
            inputCount: inputCount,
            passedCount: passedStocks.length,
            failedCount: failedStocks.length,
            passedStocks: passedStocks,
            failedStocks: failedStocks,
            filterRate: inputCount > 0 ? ((passedStocks.length / inputCount) * 100).toFixed(1) + '%' : '0%',
            processingTime: processingTime,
            conditions: level.conditions,
            color: level.color
        };
    }

    /**
     * 批处理股票
     * @param {Object} level - 层级配置
     * @param {Array} batch - 股票批次
     * @param {number} days - 数据天数
     * @returns {Array} 批处理结果
     */
    async processBatch(level, batch, days) {
        const results = [];

        for (const stock of batch) {
            try {
                const result = await this.analyzeStock(level, stock, days);
                results.push(result);
            } catch (error) {
                console.error(`分析股票 ${stock.code} 失败:`, error);
                results.push({
                    stock: stock,
                    passed: false,
                    error: error.message
                });
            }
        }

        return results;
    }

    /**
     * 分析单只股票
     * @param {Object} level - 层级配置
     * @param {Object} stock - 股票信息
     * @param {number} days - 数据天数
     * @returns {Object} 分析结果
     */
    async analyzeStock(level, stock, days) {
        const stockCode = stock.code;
        
        // 获取指定周期的数据
        const periodData = await this.getPeriodData(stockCode, level.period, days);
        
        if (!periodData || !periodData.dates || periodData.dates.length < 20) {
            return {
                stock: stock,
                passed: false,
                reason: '数据不足'
            };
        }

        // 计算指标并检查条件
        const conditionResults = [];
        
        for (const indicatorName of level.indicators) {
            const condition = level.conditions[indicatorName];
            if (!condition) continue;

            const indicatorResult = await this.calculateIndicator(
                stockCode, 
                level.period, 
                indicatorName, 
                periodData
            );

            const conditionMet = this.checkCondition(indicatorResult, condition);
            conditionResults.push(conditionMet);
        }

        // 所有条件都必须满足
        const passed = conditionResults.length > 0 && conditionResults.every(r => r);

        return {
            stock: stock,
            passed: passed,
            reason: passed ? '通过筛选' : '不满足条件',
            indicators: level.indicators,
            conditions: level.conditions
        };
    }

    /**
     * 获取指定周期的数据
     * @param {string} stockCode - 股票代码
     * @param {string} period - 周期
     * @param {number} days - 天数
     * @returns {Object} 周期数据
     */
    async getPeriodData(stockCode, period, days) {
        const cacheKey = `${stockCode}_${period}_${days}`;
        
        if (this.dataCache.has(cacheKey)) {
            return this.dataCache.get(cacheKey);
        }

        try {
            const multiPeriodData = this.dataSimulator.getBatchMultiPeriodData([stockCode], days, [period]);
            const stockData = multiPeriodData[stockCode];
            const periodData = stockData && stockData[period] ? stockData[period].data : null;
            
            // 缓存数据
            this.dataCache.set(cacheKey, periodData);
            
            return periodData;
        } catch (error) {
            console.error(`获取 ${stockCode} ${period} 数据失败:`, error);
            return null;
        }
    }

    /**
     * 计算技术指标
     * @param {string} stockCode - 股票代码
     * @param {string} period - 周期
     * @param {string} indicatorName - 指标名称
     * @param {Object} periodData - 周期数据
     * @returns {Object} 指标结果
     */
    async calculateIndicator(stockCode, period, indicatorName, periodData) {
        const cacheKey = `${stockCode}_${period}_${indicatorName}`;
        
        if (this.indicatorCache.has(cacheKey)) {
            return this.indicatorCache.get(cacheKey);
        }

        let result = null;

        try {
            switch (indicatorName) {
                case 'kdj':
                    result = this.indicators.kdj.calculate({
                        high: periodData.high,
                        low: periodData.low,
                        close: periodData.close
                    });
                    break;
                case 'macd':
                    result = this.indicators.macd.calculate(periodData.close);
                    break;
                default:
                    throw new Error(`不支持的指标: ${indicatorName}`);
            }

            // 缓存结果
            this.indicatorCache.set(cacheKey, result);
            
        } catch (error) {
            console.error(`计算 ${stockCode} ${indicatorName} 指标失败:`, error);
        }

        return result;
    }

    /**
     * 检查条件是否满足
     * @param {Object} indicatorResult - 指标结果
     * @param {Object} condition - 条件配置
     * @returns {boolean} 是否满足条件
     */
    checkCondition(indicatorResult, condition) {
        if (!indicatorResult) return false;

        const { type, threshold } = condition;

        switch (type) {
            case 'j_above_50':
                const j = indicatorResult.j;
                return j && j.length > 0 && j[j.length - 1] > 50;
                
            case 'j_above_80':
                const j80 = indicatorResult.j;
                return j80 && j80.length > 0 && j80[j80.length - 1] > 80;
                
            case 'j_below_20':
                const j20 = indicatorResult.j;
                return j20 && j20.length > 0 && j20[j20.length - 1] < 20;
                
            case 'golden_cross':
                return indicatorResult.signals && 
                       indicatorResult.signals.goldenCross && 
                       indicatorResult.signals.goldenCross.length > 0;
                       
            case 'death_cross':
                return indicatorResult.signals && 
                       indicatorResult.signals.deathCross && 
                       indicatorResult.signals.deathCross.length > 0;
                       
            case 'above_zero':
                if (indicatorResult.dif) {
                    const dif = indicatorResult.dif;
                    return dif && dif.length > 0 && dif[dif.length - 1] > 0;
                }
                return false;
                
            default:
                return false;
        }
    }

    /**
     * 停止筛选
     */
    stopScan() {
        this.isScanning = false;
    }

    /**
     * 获取筛选结果
     * @returns {Object} 筛选结果
     */
    getResults() {
        return {
            levels: this.levelResults,
            finalResults: this.finalResults,
            isScanning: this.isScanning,
            currentLevel: this.currentLevel,
            levelProgress: this.levelProgress
        };
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.dataCache.clear();
        this.indicatorCache.clear();
    }

    /**
     * 获取预设筛选方案
     * @returns {Object} 预设方案
     */
    static getPresets() {
        return {
            conservative: {
                name: '保守型',
                description: '严格筛选，高质量结果',
                levels: [
                    {
                        name: '初筛',
                        period: 'daily',
                        description: '日线KDJ超买区',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'j_above_80', threshold: 80 } },
                        color: '#3B82F6'
                    },
                    {
                        name: '复筛1',
                        period: 'weekly',
                        description: '周线金叉确认',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'golden_cross', threshold: 0 } },
                        color: '#10B981'
                    }
                ]
            },
            balanced: {
                name: '平衡型',
                description: '平衡筛选，适中结果',
                levels: [
                    {
                        name: '初筛',
                        period: 'daily',
                        description: '日线KDJ中性区',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'j_above_50', threshold: 50 } },
                        color: '#3B82F6'
                    },
                    {
                        name: '复筛1',
                        period: 'weekly',
                        description: '周线趋势向上',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'j_above_50', threshold: 50 } },
                        color: '#10B981'
                    },
                    {
                        name: '复筛2',
                        period: 'monthly',
                        description: '月线长期趋势',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'j_above_50', threshold: 50 } },
                        color: '#F59E0B'
                    }
                ]
            },
            aggressive: {
                name: '激进型',
                description: '宽松筛选，更多机会',
                levels: [
                    {
                        name: '初筛',
                        period: 'daily',
                        description: '日线KDJ超卖反弹',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'j_below_20', threshold: 20 } },
                        color: '#3B82F6'
                    },
                    {
                        name: '复筛1',
                        period: 'weekly',
                        description: '周线金叉机会',
                        indicators: ['kdj'],
                        conditions: { kdj: { type: 'golden_cross', threshold: 0 } },
                        color: '#10B981'
                    }
                ]
            }
        };
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MultiLevelScanner;
}
