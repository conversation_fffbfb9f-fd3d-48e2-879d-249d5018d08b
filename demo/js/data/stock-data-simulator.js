/**
 * 股票数据模拟器
 * 生成A股市场股票的模拟数据，支持技术指标扫描功能
 */

class StockDataSimulator {
    constructor() {
        this.stockList = this.generateStockList();
        this.cache = new Map();
        this.isGenerating = false;
    }

    /**
     * 生成A股股票列表
     * @returns {Array} 股票列表
     */
    generateStockList() {
        const stocks = [];
        
        // 主板股票 (000001-002999)
        const mainBoardStocks = [
            { code: '000001', name: '平安银行', market: 'SZ', industry: '银行', sector: '金融' },
            { code: '000002', name: '万科A', market: 'SZ', industry: '房地产', sector: '房地产' },
            { code: '000858', name: '五粮液', market: 'SZ', industry: '白酒', sector: '食品饮料' },
            { code: '000876', name: '新希望', market: 'SZ', industry: '农业', sector: '农林牧渔' },
            { code: '002415', name: '海康威视', market: 'SZ', industry: '安防', sector: '电子' },
            { code: '002594', name: '比亚迪', market: 'SZ', industry: '汽车', sector: '汽车' },
            { code: '002714', name: '牧原股份', market: 'SZ', industry: '养殖', sector: '农林牧渔' },
            { code: '002841', name: '视源股份', market: 'SZ', industry: '电子', sector: '电子' }
        ];

        // 创业板股票 (300001-399999)
        const gemStocks = [
            { code: '300059', name: '东方财富', market: 'SZ', industry: '证券', sector: '金融' },
            { code: '300142', name: '沃森生物', market: 'SZ', industry: '生物医药', sector: '医药生物' },
            { code: '300274', name: '阳光电源', market: 'SZ', industry: '新能源', sector: '电气设备' },
            { code: '300750', name: '宁德时代', market: 'SZ', industry: '电池', sector: '电气设备' }
        ];

        // 上海主板股票 (600000-699999)
        const shanghaiStocks = [
            { code: '600036', name: '招商银行', market: 'SH', industry: '银行', sector: '金融' },
            { code: '600519', name: '贵州茅台', market: 'SH', industry: '白酒', sector: '食品饮料' },
            { code: '600887', name: '伊利股份', market: 'SH', industry: '乳业', sector: '食品饮料' },
            { code: '601318', name: '中国平安', market: 'SH', industry: '保险', sector: '金融' },
            { code: '601398', name: '工商银行', market: 'SH', industry: '银行', sector: '金融' },
            { code: '601888', name: '中国中免', market: 'SH', industry: '免税', sector: '商贸零售' },
            { code: '603259', name: '药明康德', market: 'SH', industry: '医药外包', sector: '医药生物' },
            { code: '603501', name: '韦尔股份', market: 'SH', industry: '半导体', sector: '电子' }
        ];

        // 科创板股票 (688000-689999)
        const starStocks = [
            { code: '688111', name: '金山办公', market: 'SH', industry: '软件', sector: '计算机' },
            { code: '688981', name: '中芯国际', market: 'SH', industry: '半导体', sector: '电子' }
        ];

        stocks.push(...mainBoardStocks, ...gemStocks, ...shanghaiStocks, ...starStocks);

        // 为每只股票添加基础信息
        return stocks.map(stock => ({
            ...stock,
            basePrice: 10 + Math.random() * 90, // 基础价格 10-100元
            volatility: 0.02 + Math.random() * 0.08, // 波动率 2%-10%
            trend: Math.random() > 0.5 ? 1 : -1, // 趋势方向
            volume: Math.round(1000000 + Math.random() * 50000000), // 基础成交量
            marketCap: Math.round(100 + Math.random() * 5000) // 市值（亿元）
        }));
    }

    /**
     * 生成单只股票的历史数据
     * @param {Object} stock - 股票信息
     * @param {number} days - 天数
     * @returns {Object} 股票历史数据
     */
    generateStockData(stock, days = 120) {
        const cacheKey = `${stock.code}_${days}`;
        
        // 检查缓存
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        const data = {
            code: stock.code,
            name: stock.name,
            market: stock.market,
            industry: stock.industry,
            sector: stock.sector,
            dates: [],
            open: [],
            high: [],
            low: [],
            close: [],
            volume: [],
            turnover: []
        };

        let currentPrice = stock.basePrice;
        let currentVolume = stock.volume;
        
        // 生成趋势参数
        const trendStrength = 0.001 + Math.random() * 0.003; // 趋势强度
        const cycleLength = 20 + Math.random() * 40; // 周期长度
        
        for (let i = 0; i < days; i++) {
            // 生成日期
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            data.dates.push(date.toISOString().split('T')[0]);

            // 计算趋势影响
            const trendEffect = stock.trend * trendStrength * Math.sin(i / cycleLength * Math.PI);
            
            // 生成随机波动
            const randomChange = (Math.random() - 0.5) * stock.volatility;
            const totalChange = trendEffect + randomChange;

            // 计算开盘价
            const open = currentPrice * (1 + (Math.random() - 0.5) * 0.01);
            
            // 计算收盘价
            const close = open * (1 + totalChange);
            
            // 计算最高价和最低价
            const high = Math.max(open, close) * (1 + Math.random() * 0.02);
            const low = Math.min(open, close) * (1 - Math.random() * 0.02);

            // 生成成交量（考虑价格变化影响）
            const priceChangeEffect = Math.abs(totalChange) * 2; // 价格变化越大，成交量越大
            const volumeMultiplier = 0.5 + Math.random() + priceChangeEffect;
            const volume = Math.round(currentVolume * volumeMultiplier);

            // 计算成交额
            const avgPrice = (high + low + close) / 3;
            const turnover = Math.round(volume * avgPrice);

            // 添加数据
            data.open.push(Number(open.toFixed(2)));
            data.high.push(Number(high.toFixed(2)));
            data.low.push(Number(low.toFixed(2)));
            data.close.push(Number(close.toFixed(2)));
            data.volume.push(volume);
            data.turnover.push(turnover);

            // 更新当前价格和成交量
            currentPrice = close;
            currentVolume = volume * 0.9 + stock.volume * 0.1; // 成交量回归均值
        }

        // 缓存数据
        this.cache.set(cacheKey, data);
        
        return data;
    }

    /**
     * 批量生成多只股票数据
     * @param {Array} stockCodes - 股票代码数组
     * @param {number} days - 天数
     * @param {Function} progressCallback - 进度回调
     * @returns {Promise<Map>} 股票数据映射
     */
    async generateBatchData(stockCodes = null, days = 120, progressCallback = null) {
        const codes = stockCodes || this.stockList.map(s => s.code);
        const result = new Map();
        
        this.isGenerating = true;
        
        try {
            for (let i = 0; i < codes.length; i++) {
                if (!this.isGenerating) break; // 支持中断
                
                const code = codes[i];
                const stock = this.stockList.find(s => s.code === code);
                
                if (stock) {
                    const data = this.generateStockData(stock, days);
                    result.set(code, data);
                }
                
                // 报告进度
                if (progressCallback) {
                    progressCallback({
                        current: i + 1,
                        total: codes.length,
                        percentage: Math.round((i + 1) / codes.length * 100),
                        currentStock: stock ? stock.name : code
                    });
                }
                
                // 模拟异步处理，避免阻塞UI
                if (i % 10 === 0) {
                    await new Promise(resolve => setTimeout(resolve, 10));
                }
            }
        } finally {
            this.isGenerating = false;
        }
        
        return result;
    }

    /**
     * 获取股票列表
     * @param {Object} filters - 筛选条件
     * @returns {Array} 筛选后的股票列表
     */
    getStockList(filters = {}) {
        let stocks = [...this.stockList];
        
        // 按市场筛选
        if (filters.market) {
            stocks = stocks.filter(s => s.market === filters.market);
        }
        
        // 按行业筛选
        if (filters.industry) {
            stocks = stocks.filter(s => s.industry === filters.industry);
        }
        
        // 按板块筛选
        if (filters.sector) {
            stocks = stocks.filter(s => s.sector === filters.sector);
        }
        
        // 按市值筛选
        if (filters.minMarketCap) {
            stocks = stocks.filter(s => s.marketCap >= filters.minMarketCap);
        }
        
        if (filters.maxMarketCap) {
            stocks = stocks.filter(s => s.marketCap <= filters.maxMarketCap);
        }
        
        return stocks;
    }

    /**
     * 获取行业列表
     * @returns {Array} 行业列表
     */
    getIndustries() {
        const industries = [...new Set(this.stockList.map(s => s.industry))];
        return industries.sort();
    }

    /**
     * 获取板块列表
     * @returns {Array} 板块列表
     */
    getSectors() {
        const sectors = [...new Set(this.stockList.map(s => s.sector))];
        return sectors.sort();
    }

    /**
     * 生成实时价格数据
     * @param {string} stockCode - 股票代码
     * @returns {Object} 实时价格数据
     */
    generateRealtimeData(stockCode) {
        const stock = this.stockList.find(s => s.code === stockCode);
        if (!stock) return null;

        const historicalData = this.generateStockData(stock, 1);
        const lastPrice = historicalData.close[0];
        
        // 生成实时变化
        const change = (Math.random() - 0.5) * stock.volatility * 0.1;
        const currentPrice = lastPrice * (1 + change);
        const changeAmount = currentPrice - lastPrice;
        const changePercent = (changeAmount / lastPrice) * 100;

        return {
            code: stockCode,
            name: stock.name,
            price: Number(currentPrice.toFixed(2)),
            change: Number(changeAmount.toFixed(2)),
            changePercent: Number(changePercent.toFixed(2)),
            volume: Math.round(stock.volume * (0.8 + Math.random() * 0.4)),
            turnover: Math.round(currentPrice * stock.volume * (0.8 + Math.random() * 0.4)),
            high: Number((currentPrice * (1 + Math.random() * 0.02)).toFixed(2)),
            low: Number((currentPrice * (1 - Math.random() * 0.02)).toFixed(2)),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * 停止数据生成
     */
    stopGeneration() {
        this.isGenerating = false;
    }

    /**
     * 从日线数据聚合生成周线数据
     * @param {Object} dailyData - 日线数据
     * @returns {Object} 周线数据
     */
    aggregateToWeekly(dailyData) {
        if (!dailyData || !dailyData.dates || dailyData.dates.length === 0) {
            return { dates: [], open: [], high: [], low: [], close: [], volume: [] };
        }

        const weeklyData = {
            dates: [],
            open: [],
            high: [],
            low: [],
            close: [],
            volume: []
        };

        let weekStart = 0;
        let currentWeekHigh = -Infinity;
        let currentWeekLow = Infinity;
        let currentWeekVolume = 0;

        for (let i = 0; i < dailyData.dates.length; i++) {
            const currentDate = new Date(dailyData.dates[i]);
            const dayOfWeek = currentDate.getDay(); // 0=周日, 1=周一, ..., 6=周六

            // 更新当前周的数据
            currentWeekHigh = Math.max(currentWeekHigh, dailyData.high[i]);
            currentWeekLow = Math.min(currentWeekLow, dailyData.low[i]);
            currentWeekVolume += dailyData.volume[i];

            // 如果是周五或者是最后一天，结束当前周
            const isWeekEnd = dayOfWeek === 5 || i === dailyData.dates.length - 1;
            const nextDate = i < dailyData.dates.length - 1 ? new Date(dailyData.dates[i + 1]) : null;
            const isNextWeek = nextDate && nextDate.getDay() === 1; // 下一天是周一

            if (isWeekEnd || isNextWeek) {
                weeklyData.dates.push(dailyData.dates[i]);
                weeklyData.open.push(dailyData.open[weekStart]);
                weeklyData.high.push(currentWeekHigh);
                weeklyData.low.push(currentWeekLow);
                weeklyData.close.push(dailyData.close[i]);
                weeklyData.volume.push(currentWeekVolume);

                // 重置周数据
                weekStart = i + 1;
                currentWeekHigh = -Infinity;
                currentWeekLow = Infinity;
                currentWeekVolume = 0;
            }
        }

        return weeklyData;
    }

    /**
     * 从日线数据聚合生成月线数据
     * @param {Object} dailyData - 日线数据
     * @returns {Object} 月线数据
     */
    aggregateToMonthly(dailyData) {
        if (!dailyData || !dailyData.dates || dailyData.dates.length === 0) {
            return { dates: [], open: [], high: [], low: [], close: [], volume: [] };
        }

        const monthlyData = {
            dates: [],
            open: [],
            high: [],
            low: [],
            close: [],
            volume: []
        };

        let monthStart = 0;
        let currentMonth = new Date(dailyData.dates[0]).getMonth();
        let currentYear = new Date(dailyData.dates[0]).getFullYear();
        let currentMonthHigh = -Infinity;
        let currentMonthLow = Infinity;
        let currentMonthVolume = 0;

        for (let i = 0; i < dailyData.dates.length; i++) {
            const currentDate = new Date(dailyData.dates[i]);
            const dateMonth = currentDate.getMonth();
            const dateYear = currentDate.getFullYear();

            // 更新当前月的数据
            currentMonthHigh = Math.max(currentMonthHigh, dailyData.high[i]);
            currentMonthLow = Math.min(currentMonthLow, dailyData.low[i]);
            currentMonthVolume += dailyData.volume[i];

            // 如果月份或年份改变，或者是最后一天，结束当前月
            const isMonthEnd = dateMonth !== currentMonth || dateYear !== currentYear || i === dailyData.dates.length - 1;

            if (isMonthEnd) {
                monthlyData.dates.push(dailyData.dates[i - 1] || dailyData.dates[i]);
                monthlyData.open.push(dailyData.open[monthStart]);
                monthlyData.high.push(currentMonthHigh);
                monthlyData.low.push(currentMonthLow);
                monthlyData.close.push(dailyData.close[i - 1] || dailyData.close[i]);
                monthlyData.volume.push(currentMonthVolume);

                // 重置月数据
                monthStart = i;
                currentMonth = dateMonth;
                currentYear = dateYear;
                currentMonthHigh = dailyData.high[i];
                currentMonthLow = dailyData.low[i];
                currentMonthVolume = dailyData.volume[i];
            }
        }

        return monthlyData;
    }

    /**
     * 获取多周期股票数据
     * @param {string} stockCode - 股票代码
     * @param {number} days - 数据天数
     * @param {string} period - 周期类型：'daily', 'weekly', 'monthly'
     * @returns {Object} 多周期股票数据
     */
    getMultiPeriodData(stockCode, days = 120, period = 'daily') {
        // 首先获取日线数据
        const dailyData = this.generateStockData(stockCode, days);

        if (period === 'daily') {
            return {
                period: 'daily',
                data: dailyData,
                meta: {
                    stockCode: stockCode,
                    period: period,
                    dataPoints: dailyData.dates.length
                }
            };
        }

        let aggregatedData;
        if (period === 'weekly') {
            aggregatedData = this.aggregateToWeekly(dailyData);
        } else if (period === 'monthly') {
            aggregatedData = this.aggregateToMonthly(dailyData);
        } else {
            throw new Error(`不支持的周期类型: ${period}`);
        }

        return {
            period: period,
            data: aggregatedData,
            meta: {
                stockCode: stockCode,
                period: period,
                dataPoints: aggregatedData.dates.length,
                originalDailyPoints: dailyData.dates.length
            }
        };
    }

    /**
     * 批量获取多周期数据
     * @param {Array} stockCodes - 股票代码数组
     * @param {number} days - 数据天数
     * @param {Array} periods - 周期数组，如['daily', 'weekly', 'monthly']
     * @returns {Object} 批量多周期数据
     */
    getBatchMultiPeriodData(stockCodes, days = 120, periods = ['daily']) {
        const result = {};

        stockCodes.forEach(stockCode => {
            result[stockCode] = {};
            periods.forEach(period => {
                try {
                    result[stockCode][period] = this.getMultiPeriodData(stockCode, days, period);
                } catch (error) {
                    console.error(`获取${stockCode}的${period}数据失败:`, error);
                    result[stockCode][period] = null;
                }
            });
        });

        return result;
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * 获取缓存统计
     * @returns {Object} 缓存统计信息
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
}

// 导出股票数据模拟器
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StockDataSimulator;
} else if (typeof window !== 'undefined') {
    window.StockDataSimulator = StockDataSimulator;
}
