/**
 * MACD指标计算模块
 * 实现MACD、DIF、DEA计算和信号识别
 * 支持标准参数(12,26,9)和自定义参数
 */

class MACDIndicator {
    constructor(options = {}) {
        // 标准MACD参数
        this.defaultFastPeriod = options.fastPeriod || 12;    // 快线EMA周期
        this.defaultSlowPeriod = options.slowPeriod || 26;    // 慢线EMA周期
        this.defaultSignalPeriod = options.signalPeriod || 9; // 信号线EMA周期
        this.cache = new Map();
    }

    /**
     * 计算MACD指标
     * @param {Array} prices - 价格数组（通常是收盘价）
     * @param {number} fastPeriod - 快线周期，默认12
     * @param {number} slowPeriod - 慢线周期，默认26
     * @param {number} signalPeriod - 信号线周期，默认9
     * @returns {Object} MACD指标结果
     */
    calculate(prices, fastPeriod = this.defaultFastPeriod, slowPeriod = this.defaultSlowPeriod, signalPeriod = this.defaultSignalPeriod) {
        if (!prices || !Array.isArray(prices) || prices.length === 0) {
            throw new Error('价格数据不能为空');
        }

        const dataLength = prices.length;
        const minLength = Math.max(slowPeriod, fastPeriod) + signalPeriod;
        
        if (dataLength < minLength) {
            throw new Error(`数据长度不足，至少需要${minLength}个数据点`);
        }

        // 缓存键
        const cacheKey = `${dataLength}_${fastPeriod}_${slowPeriod}_${signalPeriod}_${JSON.stringify(prices.slice(-3))}`;
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        try {
            // 计算快线和慢线EMA
            const fastEMA = this.calculateEMA(prices, fastPeriod);
            const slowEMA = this.calculateEMA(prices, slowPeriod);

            // 计算DIF线（快线-慢线）
            const dif = [];
            for (let i = 0; i < dataLength; i++) {
                if (fastEMA[i] !== null && slowEMA[i] !== null) {
                    dif.push(fastEMA[i] - slowEMA[i]);
                } else {
                    dif.push(null);
                }
            }

            // 计算DEA线（DIF的EMA）
            const dea = this.calculateEMA(dif.filter(v => v !== null), signalPeriod);
            
            // 补齐DEA数组长度
            const deaFull = [];
            let deaIndex = 0;
            for (let i = 0; i < dataLength; i++) {
                if (dif[i] !== null && deaIndex < dea.length) {
                    deaFull.push(dea[deaIndex]);
                    deaIndex++;
                } else {
                    deaFull.push(null);
                }
            }

            // 计算MACD柱状图（DIF-DEA）*2
            const macd = [];
            for (let i = 0; i < dataLength; i++) {
                if (dif[i] !== null && deaFull[i] !== null) {
                    macd.push((dif[i] - deaFull[i]) * 2);
                } else {
                    macd.push(null);
                }
            }

            // 识别信号
            const signals = this.identifySignals(dif, deaFull, macd);

            const result = {
                dif: dif,
                dea: deaFull,
                macd: macd,
                signals: signals,
                parameters: {
                    fastPeriod: fastPeriod,
                    slowPeriod: slowPeriod,
                    signalPeriod: signalPeriod
                }
            };

            // 缓存结果
            this.cache.set(cacheKey, result);
            return result;

        } catch (error) {
            console.error('MACD计算错误:', error);
            throw new Error(`MACD计算失败: ${error.message}`);
        }
    }

    /**
     * 计算指数移动平均线(EMA)
     * @param {Array} prices - 价格数组
     * @param {number} period - 计算周期
     * @returns {Array} EMA数组
     */
    calculateEMA(prices, period) {
        if (!prices || prices.length === 0) {
            return [];
        }

        const ema = [];
        const multiplier = 2 / (period + 1);
        let sum = 0;
        let count = 0;

        for (let i = 0; i < prices.length; i++) {
            if (prices[i] === null || prices[i] === undefined) {
                ema.push(null);
                continue;
            }

            if (count < period) {
                // 初始阶段使用简单移动平均
                sum += prices[i];
                count++;
                if (count === period) {
                    ema.push(sum / period);
                } else {
                    ema.push(null);
                }
            } else {
                // 使用EMA公式
                const prevEMA = ema[i - 1];
                const currentEMA = (prices[i] * multiplier) + (prevEMA * (1 - multiplier));
                ema.push(currentEMA);
            }
        }

        return ema;
    }

    /**
     * 识别MACD信号
     * @param {Array} dif - DIF数组
     * @param {Array} dea - DEA数组
     * @param {Array} macd - MACD数组
     * @returns {Object} 信号识别结果
     */
    identifySignals(dif, dea, macd) {
        const signals = {
            goldenCross: [],      // 金叉信号
            deathCross: [],       // 死叉信号
            bullishDivergence: [], // 底背离
            bearishDivergence: [], // 顶背离
            zeroAxisCross: [],    // 零轴穿越
            current: {
                trend: 'neutral',  // 当前趋势：bullish/bearish/neutral
                position: 'zero',  // 当前位置：above/below/zero
                strength: 0        // 信号强度：0-100
            }
        };

        if (!dif || !dea || dif.length < 2) {
            return signals;
        }

        // 识别金叉死叉
        for (let i = 1; i < dif.length; i++) {
            if (dif[i] !== null && dea[i] !== null && dif[i-1] !== null && dea[i-1] !== null) {
                // 金叉：DIF从下方穿越DEA
                if (dif[i-1] <= dea[i-1] && dif[i] > dea[i]) {
                    signals.goldenCross.push({
                        index: i,
                        dif: dif[i],
                        dea: dea[i],
                        strength: this.calculateSignalStrength(dif[i], dea[i], macd[i])
                    });
                }
                
                // 死叉：DIF从上方穿越DEA
                if (dif[i-1] >= dea[i-1] && dif[i] < dea[i]) {
                    signals.deathCross.push({
                        index: i,
                        dif: dif[i],
                        dea: dea[i],
                        strength: this.calculateSignalStrength(dif[i], dea[i], macd[i])
                    });
                }

                // 零轴穿越
                if (dif[i-1] * dif[i] < 0) { // 符号改变
                    signals.zeroAxisCross.push({
                        index: i,
                        direction: dif[i] > 0 ? 'up' : 'down',
                        value: dif[i]
                    });
                }
            }
        }

        // 计算当前状态
        const lastIndex = dif.length - 1;
        if (lastIndex >= 0 && dif[lastIndex] !== null && dea[lastIndex] !== null) {
            const currentDIF = dif[lastIndex];
            const currentDEA = dea[lastIndex];
            const currentMACD = macd[lastIndex];

            // 趋势判断
            if (currentDIF > currentDEA) {
                signals.current.trend = 'bullish';
            } else if (currentDIF < currentDEA) {
                signals.current.trend = 'bearish';
            } else {
                signals.current.trend = 'neutral';
            }

            // 位置判断
            if (currentDIF > 0) {
                signals.current.position = 'above';
            } else if (currentDIF < 0) {
                signals.current.position = 'below';
            } else {
                signals.current.position = 'zero';
            }

            // 强度计算
            signals.current.strength = this.calculateSignalStrength(currentDIF, currentDEA, currentMACD);
        }

        return signals;
    }

    /**
     * 计算信号强度
     * @param {number} dif - DIF值
     * @param {number} dea - DEA值
     * @param {number} macd - MACD值
     * @returns {number} 信号强度(0-100)
     */
    calculateSignalStrength(dif, dea, macd) {
        if (dif === null || dea === null || macd === null) {
            return 0;
        }

        // 基于DIF和DEA的差值以及MACD柱状图的高度计算强度
        const difDeaDiff = Math.abs(dif - dea);
        const macdAbs = Math.abs(macd);
        
        // 简单的强度计算，可以根据实际需要调整
        const strength = Math.min(100, (difDeaDiff + macdAbs) * 100);
        
        return Math.round(strength);
    }

    /**
     * 获取最新的MACD值
     * @param {Array} prices - 价格数组
     * @param {Object} options - 参数选项
     * @returns {Object} 最新MACD值
     */
    getLatestValues(prices, options = {}) {
        const result = this.calculate(prices, options.fastPeriod, options.slowPeriod, options.signalPeriod);
        const lastIndex = result.dif.length - 1;
        
        return {
            dif: result.dif[lastIndex],
            dea: result.dea[lastIndex],
            macd: result.macd[lastIndex],
            signal: result.signals.current
        };
    }

    /**
     * 判断是否为买入信号
     * @param {Array} prices - 价格数组
     * @param {Object} options - 参数选项
     * @returns {boolean} 是否为买入信号
     */
    isBuySignal(prices, options = {}) {
        const result = this.calculate(prices, options.fastPeriod, options.slowPeriod, options.signalPeriod);
        const signals = result.signals;
        
        // 最近是否有金叉
        const recentGoldenCross = signals.goldenCross.length > 0 && 
            signals.goldenCross[signals.goldenCross.length - 1].index >= prices.length - 3;
        
        // 当前趋势是否向上
        const bullishTrend = signals.current.trend === 'bullish';
        
        // DIF是否在零轴上方
        const aboveZero = signals.current.position === 'above';
        
        return recentGoldenCross || (bullishTrend && aboveZero);
    }

    /**
     * 判断是否为卖出信号
     * @param {Array} prices - 价格数组
     * @param {Object} options - 参数选项
     * @returns {boolean} 是否为卖出信号
     */
    isSellSignal(prices, options = {}) {
        const result = this.calculate(prices, options.fastPeriod, options.slowPeriod, options.signalPeriod);
        const signals = result.signals;
        
        // 最近是否有死叉
        const recentDeathCross = signals.deathCross.length > 0 && 
            signals.deathCross[signals.deathCross.length - 1].index >= prices.length - 3;
        
        // 当前趋势是否向下
        const bearishTrend = signals.current.trend === 'bearish';
        
        // DIF是否在零轴下方
        const belowZero = signals.current.position === 'below';
        
        return recentDeathCross || (bearishTrend && belowZero);
    }

    /**
     * 清除缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MACDIndicator;
}
