<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层级复筛功能优化演示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        :root {
            --bg-primary: #0f172a;
            --bg-secondary: #1e293b;
            --bg-tertiary: #334155;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --accent-blue: #3b82f6;
            --accent-green: #10b981;
            --accent-yellow: #f59e0b;
            --accent-red: #ef4444;
            --border-color: #475569;
        }

        body {
            background-color: var(--bg-primary);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .card {
            background-color: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 0.75rem;
        }

        .input-field {
            background-color: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            color: var(--text-primary);
        }

        .input-field:focus {
            border-color: var(--accent-blue);
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn-primary {
            background-color: var(--accent-blue);
            color: white;
            transition: all 0.2s;
        }

        .btn-primary:hover {
            background-color: #2563eb;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            transition: all 0.2s;
        }

        .btn-secondary:hover {
            background-color: var(--border-color);
            color: var(--text-primary);
        }

        .quick-control-group {
            transition: all 0.2s;
        }

        .quick-control-group:hover {
            background-color: var(--bg-tertiary);
        }

        .level-config {
            transition: all 0.2s;
        }

        .level-config:hover {
            background-color: var(--bg-tertiary);
        }

        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }

        .tooltip {
            position: relative;
        }

        .tooltip:hover::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            padding: 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            white-space: nowrap;
            z-index: 1000;
            border: 1px solid var(--border-color);
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-white mb-2">
                <i class="fas fa-filter mr-3 text-blue-400"></i>
                多层级复筛功能优化演示
            </h1>
            <p class="text-gray-400">
                简化操作流程，提升用户体验，在主界面直接完成90%的筛选配置
            </p>
        </div>

        <!-- 功能特点说明 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="card p-6 text-center">
                <div class="text-green-400 text-2xl mb-3">
                    <i class="fas fa-mouse-pointer"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">一键配置</h3>
                <p class="text-gray-400 text-sm">
                    主界面直接显示预设概览，无需进入二级页面
                </p>
            </div>
            <div class="card p-6 text-center">
                <div class="text-blue-400 text-2xl mb-3">
                    <i class="fas fa-sliders-h"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">快速调整</h3>
                <p class="text-gray-400 text-sm">
                    关键参数直接修改，实时预览配置效果
                </p>
            </div>
            <div class="card p-6 text-center">
                <div class="text-purple-400 text-2xl mb-3">
                    <i class="fas fa-cogs"></i>
                </div>
                <h3 class="text-lg font-semibold mb-2">高级选项</h3>
                <p class="text-gray-400 text-sm">
                    复杂配置保留完整功能，满足专业需求
                </p>
            </div>
        </div>

        <!-- 多层级筛选面板容器 -->
        <div class="card p-6">
            <div id="multiLevelPanelContainer">
                <!-- 这里将由JavaScript动态生成多层级筛选界面 -->
            </div>
        </div>

        <!-- 使用说明 -->
        <div class="mt-8 card p-6">
            <h3 class="text-lg font-semibold mb-4">
                <i class="fas fa-info-circle mr-2 text-blue-400"></i>
                使用说明
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium mb-2 text-green-400">✨ 新功能特点</h4>
                    <ul class="text-sm text-gray-400 space-y-1">
                        <li>• 预设方案概览：直接显示层级配置详情</li>
                        <li>• 快速调整区域：常用参数一键修改</li>
                        <li>• 实时配置预览：修改后立即看到效果</li>
                        <li>• 一键重置：快速恢复原始预设配置</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium mb-2 text-blue-400">🎯 操作流程</h4>
                    <ul class="text-sm text-gray-400 space-y-1">
                        <li>1. 选择预设方案（保守型/平衡型/激进型）</li>
                        <li>2. 在快速调整区域修改关键参数</li>
                        <li>3. 预览配置效果，确认无误后开始筛选</li>
                        <li>4. 如需复杂配置，点击"高级配置"</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JavaScript文件 -->
    <script src="js/data/stock-data-simulator.js"></script>
    <script src="js/indicators/indicator-utils.js"></script>
    <script src="js/indicators/kdj-indicator.js"></script>
    <script src="js/indicators/macd-indicator.js"></script>
    <script src="js/indicators/bollinger-bands.js"></script>
    <script src="js/indicators/volume-analysis.js"></script>
    <script src="js/scanner/multi-level-scanner.js"></script>
    <script src="js/ui/multi-level-panel.js"></script>

    <script>
        // 初始化演示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('开始初始化多层级复筛演示...');
            
            // 等待依赖加载完成
            setTimeout(() => {
                try {
                    // 检查依赖
                    if (typeof MultiLevelScanner === 'undefined' || typeof MultiLevelPanel === 'undefined') {
                        console.error('依赖模块未加载完成');
                        return;
                    }

                    // 创建数据模拟器
                    const dataSimulator = new StockDataSimulator();
                    
                    // 创建多层级扫描器
                    const multiLevelScanner = new MultiLevelScanner(dataSimulator);
                    
                    // 创建多层级面板
                    const multiLevelPanel = new MultiLevelPanel('multiLevelPanelContainer');
                    multiLevelPanel.setScanner(multiLevelScanner);
                    
                    // 设置事件回调
                    multiLevelPanel.onScanStart = () => {
                        console.log('演示：多层级扫描开始');
                    };
                    
                    multiLevelPanel.onScanComplete = (results) => {
                        console.log('演示：多层级扫描完成', results);
                    };
                    
                    multiLevelPanel.onLevelComplete = (levelIndex, result) => {
                        console.log(`演示：层级 ${result.levelName} 完成`, result);
                    };
                    
                    // 默认加载平衡型预设
                    setTimeout(() => {
                        const presetSelector = document.getElementById('presetSelector');
                        if (presetSelector) {
                            presetSelector.value = 'balanced';
                            presetSelector.dispatchEvent(new Event('change'));
                        }
                    }, 500);
                    
                    console.log('多层级复筛演示初始化完成');
                    
                } catch (error) {
                    console.error('初始化失败:', error);
                }
            }, 1000);
        });
    </script>
</body>
</html>
